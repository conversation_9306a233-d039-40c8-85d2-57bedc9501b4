# Feature: Enhanced Analysis Companion Chat with MCP-use-ts Integration

## Persona & Stack
- **Persona**: Senior Full-Stack Developer with AI/LLM Integration & MCP Expertise
- **Frameworks**: React 19, Vite, Hono APAI, Tailwind CSS, ShadCN UI
- **Languages**: TypeScript, Node.js
- **DB/API**: Supabase PostgreSQL, Firebase Auth
- **AI/MCP**: mcp-use-ts v0.1.14, OpenAI GPT-4o, LangChain.js, Vercel AI SDK
- **Package Manager**: pnpm
- **MCP Servers**: @modelcontextprotocol/server-everything, @playwright/mcp, filesystem tools

## Goal
Build an intelligent analysis companion chat interface leveraging mcp-use-ts library's advanced capabilities including MCPAgent with conversation memory, real-time streaming via streamEvents(), AI SDK integration, and multi-server MCP support for comprehensive analysis tasks like web browsing, file operations, and data processing.

## Scope & Constraints
- **Scope**: Create new chat interface in `ui/src/components/chat/` and streaming API in `server/src/routes/`
- **Integration**: Seamlessly integrate with existing Firebase Auth and Supabase database
- **Limitations**: No modifications to core authentication, database schemas, or existing components
- **Environment**: Leverage existing development setup with embedded PostgreSQL and Firebase emulators
- **Node Version**: Requires Node.js >=22.0.0 for mcp-use-ts compatibility

## Context
- **Code Injection**: Reference existing ShadCN UI components from `ui/src/components/ui/` for consistent styling
- **Memory Recall**: Recall and reference relevant memories from memory folder for architectural consistency with volo-app template patterns
- **Library Reference**: [mcp-use-ts](https://github.com/mcp-use/mcp-use-ts) - Unified MCP Client Library with LangChain.js integration
- **Examples Reference**: [mcp-use-ts examples](https://github.com/mcp-use/mcp-use-ts/tree/main/examples) - Chat, streaming, and AI SDK integration patterns

## Plan
1. **Dependencies & Environment Setup**
   - Install mcp-use-ts, langchain, @langchain/openai, ai, dotenv
   - Configure OpenAI API key in environment
   - Set up MCP server configuration with multi-server support
   - Verify Node.js >=22.0.0 compatibility

2. **Backend Implementation (Hono API)**
   - Create `/api/chat` streaming endpoint using AI SDK LangChainAdapter
   - Implement MCPAgent with conversation memory and streamEvents()
   - Configure multiple MCP servers (everything, playwright, filesystem)
   - Add proper session management and cleanup
   - Implement streamEventsToAISDKWithTools for tool visibility

3. **Frontend Components (React 19 + ShadCN)**
   - Create chat interface using useCompletion hook from AI SDK
   - Implement real-time token-by-token streaming display
   - Add conversation memory management with clear history
   - Create tool usage indicators (🔧 Using tool: X, ✅ Tool completed: X)
   - Build file upload and URL analysis features

4. **Integration & Testing**
   - Connect streaming chat to backend API
   - Test multi-server MCP functionality
   - Validate conversation memory persistence
   - Ensure proper authentication integration
   - Test responsive design and accessibility

## Requirements

### Backend Requirements
- **MCPAgent Configuration**: 
  ```ts
  new MCPAgent({
    llm: new ChatOpenAI({ model: 'gpt-4o' }),
    client: MCPClient.fromDict(config),
    maxSteps: 15,
    memoryEnabled: true,
    useServerManager: true // for multi-server support
  })
  ```
- **MCP Servers Configuration**:
  ```json
  {
    "mcpServers": {
      "everything": { "command": "npx", "args": ["-y", "@modelcontextprotocol/server-everything"] },
      "playwright": { "command": "npx", "args": ["@playwright/mcp@latest"] },
      "filesystem": { "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem"] }
    }
  }
  ```
- **Streaming API**: Use LangChainAdapter.toDataStreamResponse() for AI SDK compatibility
- **Session Management**: Implement proper client.closeAllSessions() cleanup
- **Tool Restrictions**: Configure disallowedTools for security if needed

### Frontend Requirements
- **Streaming Chat UI**: Use useCompletion hook with real-time token streaming
- **Tool Visibility**: Display tool usage with enhanced streamEventsToAISDKWithTools
- **Conversation Memory**: 
  - Persistent conversation state
  - Clear history functionality (agent.clearConversationHistory())
  - Memory-enabled chat sessions
- **Analysis Features**:
  - File upload for document analysis
  - URL input for web page analysis via Playwright MCP
  - Real-time tool execution feedback
- **Responsive Design**: Mobile-first with ShadCN UI components

### Technical Requirements
- **Error Handling**: Graceful MCP server connection failures and tool errors
- **Performance**: Efficient streaming with proper cleanup and debouncing
- **Security**: Input validation and MCP tool output sanitization
- **Accessibility**: ARIA labels, keyboard navigation, screen reader support

## External Libraries
- [mcp-use-ts](https://github.com/mcp-use/mcp-use-ts) - Unified MCP Client Library v0.1.14
- [LangChain.js](https://js.langchain.com/) - LLM application framework
- [Vercel AI SDK](https://sdk.vercel.ai/) - Streaming UI toolkit with React hooks
- [@langchain/openai](https://www.npmjs.com/package/@langchain/openai) - OpenAI LangChain integration
- [@modelcontextprotocol/server-everything](https://www.npmjs.com/package/@modelcontextprotocol/server-everything) - Comprehensive MCP server
- [@playwright/mcp](https://www.npmjs.com/package/@playwright/mcp) - Browser automation MCP server

## Verification & Output

### Expected Files
- `server/src/routes/chat.ts` - Streaming chat API with AI SDK integration
- `server/src/lib/mcp-config.ts` - MCP server configuration and utilities
- `server/src/lib/mcp-agent.ts` - MCPAgent setup with memory and streaming
- `ui/src/components/chat/ChatInterface.tsx` - Main chat component with useCompletion
- `ui/src/components/chat/MessageBubble.tsx` - Message display component
- `ui/src/components/chat/ToolIndicator.tsx` - Tool usage visualization
- `ui/src/components/chat/FileUpload.tsx` - File analysis upload component
- `ui/src/hooks/useAnalysisChat.ts` - Custom chat hook with MCP features
- `ui/src/pages/AnalysisChat.tsx` - Chat page with authentication

### Verification Plan
1. **Unit Tests**: Test MCPAgent configuration, streaming API, and chat components
2. **Integration Tests**: 
   - Verify streamEvents() to AI SDK conversion
   - Test multi-server MCP functionality
   - Validate conversation memory persistence
3. **Manual Testing**:
   - Test real-time streaming with tool indicators
   - Verify file upload and URL analysis
   - Test conversation memory and history clearing
   - Validate authentication integration
4. **Performance Testing**: Ensure efficient streaming and proper MCP session cleanup
5. **Accessibility Testing**: Verify keyboard navigation and screen reader support

### Success Criteria
- Chat interface streams responses token-by-token using AI SDK
- MCP tools execute successfully with visual indicators
- Conversation memory persists across messages and sessions
- File and URL analysis work via MCP servers
- Multi-server MCP configuration functions properly
- Authentication integrates seamlessly
- Interface is responsive and accessible
- Proper cleanup prevents memory leaks and hanging MCP sessions
