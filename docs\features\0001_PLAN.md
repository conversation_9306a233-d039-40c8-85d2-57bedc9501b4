# Feature Plan: Enhanced Analysis Companion Chat with MCP-use-ts Integration

## Description
Build an intelligent analysis companion chat interface leveraging mcp-use-ts library's advanced capabilities including MCPAgent with conversation memory, real-time streaming via streamEvents(), AI SDK integration, and multi-server MCP support for comprehensive analysis tasks like web browsing, file operations, and data processing.

**Template Alignment**: This feature follows the volo-app template's component-first design philosophy, vertical slice architecture, and decoupled frontend/backend patterns as documented in memory-docs/architecture/system-overview.md.

## Technical Requirements

### Dependencies & Environment
- **Node.js**: Requires >=22.0.0 for mcp-use-ts compatibility (template currently supports 20+)
- **New Dependencies**:
  - `mcp-use-ts@0.1.14` - Unified MCP Client Library with LangChain.js integration
  - `@langchain/openai` - OpenAI LangChain integration
  - `langchain` - LLM application framework
  - `ai` - Vercel AI SDK for streaming UI toolkit with React hooks
  - `dotenv` - Environment configuration (server only, already exists)
- **MCP Servers**:
  - `@modelcontextprotocol/server-everything` - Comprehensive MCP server
  - `@playwright/mcp` - Browser automation MCP server
  - `@modelcontextprotocol/server-filesystem` - File system tools

### Environment Configuration
- **OpenAI API Key**: Required in server environment variables (follows template's env pattern)
- **MCP Server Configuration**: Multi-server setup with everything, playwright, and filesystem servers
- **Template Compliance**: Uses existing environment variable patterns from memory-docs/references/development-checklist.md

## Files to Create/Modify

### Backend Files (server/src/)

#### server/src/routes/chat.ts
- **Purpose**: Streaming chat API endpoint using AI SDK LangChainAdapter
- **Integration**: Uses existing `authMiddleware` from `server/src/middleware/auth.ts`
- **Functionality**: 
  - Implements `/api/v1/chat` POST endpoint for streaming responses
  - Uses LangChainAdapter.toDataStreamResponse() for AI SDK compatibility
  - Integrates with MCPAgent for conversation memory and tool execution
  - Implements streamEventsToAISDKWithTools for tool visibility

#### server/src/lib/mcp-config.ts
- **Purpose**: MCP server configuration and utilities
- **Functionality**:
  - Defines MCP servers configuration object with command/args for each server
  - Provides utility functions for MCP client initialization
  - Handles multi-server support with useServerManager: true

#### server/src/lib/mcp-agent.ts
- **Purpose**: MCPAgent setup with memory and streaming capabilities
- **Functionality**:
  - Configures MCPAgent with ChatOpenAI model (gpt-4o)
  - Enables conversation memory (memoryEnabled: true)
  - Sets maxSteps: 15 for tool execution
  - Implements proper session cleanup with client.closeAllSessions()

### Frontend Files (ui/src/)

#### ui/src/components/chat/ChatInterface.tsx
- **Purpose**: Main chat component using useCompletion hook from AI SDK
- **Integration**: Uses existing ShadCN UI components from `ui/src/components/ui/`
- **Functionality**:
  - Real-time token-by-token streaming display
  - Conversation memory management with clear history
  - Authentication integration using existing Firebase Auth context
  - Responsive design with mobile-first approach

#### ui/src/components/chat/MessageBubble.tsx
- **Purpose**: Individual message display component
- **Integration**: Follows existing ShadCN UI patterns from `ui/src/components/ui/`
- **Functionality**:
  - Displays user and assistant messages with proper styling
  - Supports markdown rendering for rich content
  - Implements proper accessibility with ARIA labels

#### ui/src/components/chat/ToolIndicator.tsx
- **Purpose**: Tool usage visualization component
- **Functionality**:
  - Displays tool execution status (🔧 Using tool: X, ✅ Tool completed: X)
  - Shows real-time tool execution feedback
  - Enhanced streamEventsToAISDKWithTools integration

#### ui/src/components/chat/FileUpload.tsx
- **Purpose**: File analysis upload component
- **Integration**: Uses existing ShadCN UI Input component
- **Functionality**:
  - File upload interface for document analysis
  - URL input for web page analysis via Playwright MCP
  - Input validation and file type restrictions

#### ui/src/hooks/useAnalysisChat.ts
- **Purpose**: Custom chat hook with MCP-specific features
- **Integration**: Uses AI SDK useCompletion hook
- **Functionality**:
  - Manages chat state and streaming responses
  - Handles conversation memory persistence
  - Implements error handling for MCP server failures
  - Provides clear history functionality

#### ui/src/pages/AnalysisChat.tsx
- **Purpose**: Chat page component with authentication
- **Integration**: Uses existing Firebase Auth patterns from codebase
- **Functionality**:
  - Protected route requiring authentication
  - Renders ChatInterface with proper layout
  - Handles authentication state and redirects

## Implementation Phases

### Phase 1: Backend Infrastructure
1. Install required dependencies in server package
2. Create MCP configuration and agent setup files
3. Implement streaming chat API endpoint with authentication
4. Configure multi-server MCP support

### Phase 2: Frontend Components  
1. Install required dependencies in UI package
2. Create chat interface components using ShadCN patterns
3. Implement custom hooks for chat functionality
4. Create file upload and tool indicator components

### Phase 3: Integration & Testing
1. Connect frontend to streaming backend API
2. Test multi-server MCP functionality
3. Validate conversation memory persistence
4. Ensure authentication integration works properly

## Integration Points

### Existing Authentication
- **Middleware**: Leverages existing `authMiddleware` from `server/src/middleware/auth.ts`
- **Frontend Auth**: Integrates with existing Firebase Auth setup in `ui/src/lib/firebase.ts`
- **User Context**: Uses existing user context patterns for protected routes

### Existing UI Components
- **ShadCN Components**: Reuses Button, Input, Separator from `ui/src/components/ui/`
- **Styling**: Follows existing Tailwind CSS patterns and ShadCN "new-york" style
- **Icons**: Uses existing Lucide React icon library

### API Structure
- **Route Mounting**: Follows existing pattern in `server/src/api.ts` with protected routes
- **Error Handling**: Consistent with existing API error response patterns
- **CORS & Middleware**: Uses existing middleware stack

## Security Considerations
- **Input Validation**: Sanitize user inputs and MCP tool outputs
- **Tool Restrictions**: Configure disallowedTools for security if needed
- **Authentication**: All chat endpoints require valid Firebase authentication
- **Session Management**: Proper cleanup prevents memory leaks and hanging MCP sessions

## Performance Considerations
- **Streaming**: Efficient token-by-token streaming with proper debouncing
- **Memory Management**: Conversation memory with configurable limits
- **Session Cleanup**: Automatic MCP session cleanup on disconnect
- **Error Recovery**: Graceful handling of MCP server connection failures
