# API Reference (v1)

## Base URL
- Local: `http://localhost:8787`
- Production: Cloudflare Worker URL (set `VITE_API_URL` for UI if needed)

All JSON responses include an ISO `timestamp` where relevant. Errors return `{ error, details? }`.

## Public Endpoints

### GET /api/v1/hello
Response
```json
{ "message": "Hello from <PERSON><PERSON>!" }
```

### GET /api/v1/db-test
Checks DB connectivity and returns sample users.

Response 200
```json
{
  "message": "Database connection successful!",
  "users": [ { "id": "...", "email": "..." } ],
  "connectionHealthy": true,
  "usingLocalDatabase": true,
  "timestamp": "2025-08-11T00:00:00.000Z"
}
```

Response 500
```json
{ "error": "Database connection failed", "details": "..." }
```

## Protected Endpoints

All protected routes require header:
`Authorization: Bearer <Firebase_ID_Token>`

### GET /api/v1/protected/me
Returns the authenticated user from DB.

Response 200
```json
{
  "user": { "id": "...", "email": "...", "display_name": null, "photo_url": null },
  "message": "You are authenticated!"
}
```

Response 401
```json
{ "error": "Unauthorized" }
```

## cURL Examples

```bash
# Public hello
curl -s "http://localhost:8787/api/v1/hello" | jq .

# DB test
curl -s "http://localhost:8787/api/v1/db-test" | jq .

# Protected: replace TOKEN with Firebase ID token
curl -s -H "Authorization: Bearer TOKEN" "http://localhost:8787/api/v1/protected/me" | jq .
```



